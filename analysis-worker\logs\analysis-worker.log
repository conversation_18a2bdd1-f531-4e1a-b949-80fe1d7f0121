{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 10:28:28","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:28:28","userEmail":"<EMAIL>","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","version":"1.0.0"}
{"errors":["\"assessmentData.multipleIntelligences\" is required","\"assessmentData.cognitiveStyleIndex\" is required"],"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"error":"Invalid message format: \"assessmentData.multipleIntelligences\" is required, \"assessmentData.cognitiveStyleIndex\" is required","jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Failed to process assessment job","processingTime":"5ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:28:28","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","version":"1.0.0"}
{"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:28:28","userEmail":"<EMAIL>","userId":"test-user-*************","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Failed to process assessment job","processingTime":"2ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:28:28","userId":"test-user-*************","version":"1.0.0"}
{"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:28:28","userEmail":"<EMAIL>","userId":"test-user-*************","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:28:28","userId":"test-user-*************","version":"1.0.0"}
{"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"delay":5000,"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"info","message":"Retry message published successfully","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","version":"1.0.0"}
{"delay":5000,"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"info","message":"Retry message published successfully","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","version":"1.0.0"}
{"delay":5000,"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"info","message":"Retry message published successfully","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"info","message":"Processing assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","userEmail":"<EMAIL>","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","version":"1.0.0"}
{"errors":["\"assessmentData.multipleIntelligences\" is required","\"assessmentData.cognitiveStyleIndex\" is required"],"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:33","version":"1.0.0"}
{"error":"Invalid message format: \"assessmentData.multipleIntelligences\" is required, \"assessmentData.cognitiveStyleIndex\" is required","jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","version":"1.0.0"}
{"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"info","message":"Processing assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","userEmail":"<EMAIL>","userId":"test-user-*************","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:33","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Failed to process assessment job","processingTime":"0ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","userId":"test-user-*************","version":"1.0.0"}
{"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"info","message":"Processing assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","userEmail":"<EMAIL>","userId":"test-user-*************","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:33","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","userId":"test-user-*************","version":"1.0.0"}
{"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","version":"1.0.0"}
{"delay":10000,"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"info","message":"Retry message published successfully","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","version":"1.0.0"}
{"delay":10000,"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"info","message":"Retry message published successfully","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","version":"1.0.0"}
{"delay":10000,"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"info","message":"Retry message published successfully","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"info","message":"Processing assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","userEmail":"<EMAIL>","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","version":"1.0.0"}
{"errors":["\"assessmentData.multipleIntelligences\" is required","\"assessmentData.cognitiveStyleIndex\" is required"],"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:43","version":"1.0.0"}
{"error":"Invalid message format: \"assessmentData.multipleIntelligences\" is required, \"assessmentData.cognitiveStyleIndex\" is required","jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","version":"1.0.0"}
{"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"info","message":"Processing assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","userEmail":"<EMAIL>","userId":"test-user-*************","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:43","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Failed to process assessment job","processingTime":"0ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","userId":"test-user-*************","version":"1.0.0"}
{"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"info","message":"Processing assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","userEmail":"<EMAIL>","userId":"test-user-*************","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:43","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","userId":"test-user-*************","version":"1.0.0"}
{"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:28:58","version":"1.0.0"}
{"delay":20000,"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"info","message":"Retry message published successfully","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"delay":20000,"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"info","message":"Retry message published successfully","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"delay":20000,"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"info","message":"Retry message published successfully","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"info","message":"Processing assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","userEmail":"<EMAIL>","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","version":"1.0.0"}
{"errors":["\"assessmentData.multipleIntelligences\" is required","\"assessmentData.cognitiveStyleIndex\" is required"],"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"error":"Invalid message format: \"assessmentData.multipleIntelligences\" is required, \"assessmentData.cognitiveStyleIndex\" is required","jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","version":"1.0.0"}
{"error":"Invalid message format: \"assessmentData.multipleIntelligences\" is required, \"assessmentData.cognitiveStyleIndex\" is required","jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"info","message":"Processing assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","userEmail":"<EMAIL>","userId":"test-user-*************","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","userId":"test-user-*************","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"********-f5cf-438a-9c0b-f8088f3dc608","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"info","message":"Processing assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","userEmail":"<EMAIL>","userId":"test-user-*************","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Failed to process assessment job","processingTime":"0ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","userId":"test-user-*************","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"********-041c-4180-89ea-f973bde1d5ce","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 10:29:49","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 10:29:49","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 10:29:49","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 10:29:49","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 10:29:49","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 10:29:49","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:30:19","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:30:49","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:31:19","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:31:49","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:31:57","userEmail":"<EMAIL>","userId":"test-user-1752723117024","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:31:57","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Failed to process assessment job","processingTime":"5ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:31:57","userId":"test-user-1752723117024","version":"1.0.0"}
{"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:31:57","version":"1.0.0"}
{"delay":5000,"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"info","message":"Retry message published successfully","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:32:02","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"info","message":"Processing assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:32:02","userEmail":"<EMAIL>","userId":"test-user-1752723117024","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:32:02","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Failed to process assessment job","processingTime":"2ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:32:02","userId":"test-user-1752723117024","version":"1.0.0"}
{"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:32:02","version":"1.0.0"}
{"delay":10000,"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"info","message":"Retry message published successfully","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:32:12","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"info","message":"Processing assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:32:12","userEmail":"<EMAIL>","userId":"test-user-1752723117024","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:32:12","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:32:12","userId":"test-user-1752723117024","version":"1.0.0"}
{"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:32:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:32:19","version":"1.0.0"}
{"delay":20000,"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"info","message":"Retry message published successfully","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:32:32","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"info","message":"Processing assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:32:32","userEmail":"<EMAIL>","userId":"test-user-1752723117024","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:32:32","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Failed to process assessment job","processingTime":"2ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:32:32","userId":"test-user-1752723117024","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 10:32:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:32:49","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:33:19","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:33:48","userEmail":"<EMAIL>","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 10:33:48","userEmail":"<EMAIL>","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 10:33:48","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-17 10:33:48","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:33:49","version":"1.0.0"}
{"error":"exception TypeError: fetch failed sending request","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to generate persona profile","service":"analysis-worker","timestamp":"2025-07-17 10:33:54","version":"1.0.0"}
{"error":"exception TypeError: fetch failed sending request","level":"error","maxRetries":3,"message":"All retry attempts failed for AI persona generation","service":"analysis-worker","timestamp":"2025-07-17 10:33:54","version":"1.0.0"}
{"error":"exception TypeError: fetch failed sending request","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Assessment processing failed","service":"analysis-worker","stack":"Error: exception TypeError: fetch failed sending request\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\node_modules\\@google\\genai\\dist\\node\\index.js:6178:19\n    at async Models.generateContent (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\node_modules\\@google\\genai\\dist\\node\\index.js:2874:20)\n    at async Object.generatePersonaProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\src\\services\\aiService.js:198:22)\n    at async withRetry (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\src\\utils\\errorHandler.js:134:14)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\src\\processors\\assessmentProcessor.js:32:30","timestamp":"2025-07-17 10:33:54","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 10:33:54","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Assessment processing failed: exception TypeError: fetch failed sending request","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to process assessment job","processingTime":"6237ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:33:54","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:33:54","version":"1.0.0"}
{"delay":5000,"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Retry message published successfully","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:34:00","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Processing assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:34:00","userEmail":"<EMAIL>","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 10:34:00","userEmail":"<EMAIL>","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 10:34:00","version":"1.0.0"}
{"candidatesTokenCount":936,"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"AI response received","responseLength":4013,"service":"analysis-worker","thoughtsTokenCount":2532,"timestamp":"2025-07-17 10:34:18","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 10:34:18","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Architect","service":"analysis-worker","timestamp":"2025-07-17 10:34:18","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-17 10:34:18","url":"/archive/results","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-17 10:34:18","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"delay":5000,"error":"","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:34:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:34:19","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Architect","service":"analysis-worker","timestamp":"2025-07-17 10:34:23","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-17 10:34:23","url":"/archive/results","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-17 10:34:23","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"delay":10000,"error":"","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:34:23","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Architect","service":"analysis-worker","timestamp":"2025-07-17 10:34:33","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-17 10:34:33","url":"/archive/results","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-17 10:34:33","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"delay":20000,"error":"","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:34:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:34:49","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Architect","service":"analysis-worker","timestamp":"2025-07-17 10:34:53","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-17 10:34:53","url":"/archive/results","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-17 10:34:53","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:34:53","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 10:34:53","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 10:34:53","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Assessment processing failed: ","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to process assessment job","processingTime":"53179ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:34:53","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:34:53","version":"1.0.0"}
{"delay":10000,"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Retry message published successfully","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:35:03","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Processing assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:35:03","userEmail":"<EMAIL>","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 10:35:03","userEmail":"<EMAIL>","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 10:35:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:35:19","version":"1.0.0"}
{"candidatesTokenCount":941,"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"AI response received","responseLength":4166,"service":"analysis-worker","thoughtsTokenCount":3139,"timestamp":"2025-07-17 10:35:24","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 10:35:24","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 10:35:24","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:35:25","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:35:25","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:35:25","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 10:35:30","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:35:30","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:35:30","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:35:30","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 10:35:40","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:35:40","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:35:40","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"delay":20000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:35:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:35:49","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 10:36:00","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:00","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:00","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:36:00","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 10:36:00","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 10:36:00","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to process assessment job","processingTime":"57101ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:36:00","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:36:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:36:19","version":"1.0.0"}
{"delay":20000,"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Retry message published successfully","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:36:20","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Processing assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:36:20","userEmail":"<EMAIL>","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 10:36:20","userEmail":"<EMAIL>","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 10:36:20","version":"1.0.0"}
{"candidatesTokenCount":1004,"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"AI response received","responseLength":4155,"service":"analysis-worker","thoughtsTokenCount":2949,"timestamp":"2025-07-17 10:36:40","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 10:36:40","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem Solver","service":"analysis-worker","timestamp":"2025-07-17 10:36:40","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:40","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:40","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:36:40","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem Solver","service":"analysis-worker","timestamp":"2025-07-17 10:36:45","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:45","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:45","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:36:45","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:36:49","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem Solver","service":"analysis-worker","timestamp":"2025-07-17 10:36:55","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:55","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:55","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"delay":20000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:36:55","version":"1.0.0"}
{"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem Solver","service":"analysis-worker","timestamp":"2025-07-17 10:37:15","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:37:15","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:37:15","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:37:15","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 10:37:15","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 10:37:15","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to process assessment job","processingTime":"54924ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:37:15","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 10:37:15","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:37:19","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:37:49","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:38:19","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:38:49","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:39:19","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:39:49","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:40:19","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:40:49","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:41:19","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:41:49","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:42:19","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:42:49","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 10:42:57","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 10:42:57","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 10:42:58","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 10:42:58","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 10:42:58","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 10:42:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:43:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:43:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:44:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:44:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:45:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:45:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:46:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:46:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:47:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:47:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:48:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:48:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:49:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:49:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:50:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:50:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:51:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:51:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:52:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:52:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:53:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:53:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:54:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:54:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:55:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:55:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:56:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:56:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:57:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:57:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:58:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:58:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:59:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 10:59:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:00:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:00:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 11:01:03","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 11:01:03","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 11:01:03","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 11:01:03","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 11:01:03","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 11:01:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:01:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:01:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:01:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:02:03","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:02:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 11:02:39","userEmail":"<EMAIL>","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 11:02:39","userEmail":"<EMAIL>","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 11:02:39","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-17 11:02:39","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:03:03","version":"1.0.0"}
{"candidatesTokenCount":1017,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"AI response received","responseLength":4422,"service":"analysis-worker","thoughtsTokenCount":3495,"timestamp":"2025-07-17 11:03:05","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 11:03:05","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Systems Architect","service":"analysis-worker","timestamp":"2025-07-17 11:03:05","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:05","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:05","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:03:05","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Systems Architect","service":"analysis-worker","timestamp":"2025-07-17 11:03:10","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:10","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:10","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:03:10","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Systems Architect","service":"analysis-worker","timestamp":"2025-07-17 11:03:20","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:20","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:20","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":20000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:03:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:03:33","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Systems Architect","service":"analysis-worker","timestamp":"2025-07-17 11:03:40","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:40","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:40","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:03:40","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:03:40","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:03:40","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to process assessment job","processingTime":"60900ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 11:03:40","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:03:40","version":"1.0.0"}
{"delay":5000,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Retry message published successfully","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:03:45","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Processing assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:03:45","userEmail":"<EMAIL>","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 11:03:45","userEmail":"<EMAIL>","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 11:03:45","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:04:03","version":"1.0.0"}
{"candidatesTokenCount":995,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"AI response received","responseLength":4301,"service":"analysis-worker","thoughtsTokenCount":2359,"timestamp":"2025-07-17 11:04:05","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 11:04:05","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:04:05","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:04:05","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:04:05","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:04:05","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:04:10","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:04:10","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:04:10","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:04:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 11:13:38","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 11:13:38","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 11:13:38","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 11:13:38","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Processing assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:13:38","userEmail":"<EMAIL>","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 11:13:38","userEmail":"<EMAIL>","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 11:13:38","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-17 11:13:38","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 11:13:38","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 11:13:38","version":"1.0.0"}
{"candidatesTokenCount":1062,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"AI response received","responseLength":4330,"service":"analysis-worker","thoughtsTokenCount":2627,"timestamp":"2025-07-17 11:13:57","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 11:13:57","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:13:57","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:13:57","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:13:57","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:13:57","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:14:02","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:02","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:02","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:14:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:14:08","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:14:12","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:12","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:12","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":20000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:14:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 11:14:23","userEmail":"<EMAIL>","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 11:14:23","userEmail":"<EMAIL>","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 11:14:23","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:14:32","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:32","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:32","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:14:32","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:14:32","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:14:32","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to process assessment job","processingTime":"54347ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:14:32","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:14:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:14:38","version":"1.0.0"}
{"candidatesTokenCount":1080,"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"AI response received","responseLength":4769,"service":"analysis-worker","thoughtsTokenCount":2682,"timestamp":"2025-07-17 11:14:41","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 11:14:41","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Systems Architect","service":"analysis-worker","timestamp":"2025-07-17 11:14:41","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:41","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:41","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:14:41","version":"1.0.0"}
{"delay":10000,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Retry message published successfully","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:14:42","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Processing assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:14:42","userEmail":"<EMAIL>","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 11:14:42","userEmail":"<EMAIL>","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 11:14:42","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Systems Architect","service":"analysis-worker","timestamp":"2025-07-17 11:14:46","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:46","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:46","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:14:46","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Systems Architect","service":"analysis-worker","timestamp":"2025-07-17 11:14:56","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:56","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:56","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"delay":20000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:14:56","version":"1.0.0"}
{"candidatesTokenCount":843,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"AI response received","responseLength":3703,"service":"analysis-worker","thoughtsTokenCount":2350,"timestamp":"2025-07-17 11:15:00","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 11:15:00","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:15:00","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:00","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:00","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:15:00","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:15:05","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:05","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:05","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:15:05","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:15:08","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:15:15","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:15","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:15","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":20000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:15:15","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Systems Architect","service":"analysis-worker","timestamp":"2025-07-17 11:15:16","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-17 11:15:16","url":"/archive/results","version":"1.0.0"}
{"error":"","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-17 11:15:16","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:15:16","version":"1.0.0"}
{"error":"","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:15:16","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:15:16","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Assessment processing failed: ","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to process assessment job","processingTime":"53969ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 11:15:16","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:15:16","version":"1.0.0"}
{"delay":5000,"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Retry message published successfully","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:15:21","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Processing assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:15:21","userEmail":"<EMAIL>","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 11:15:21","userEmail":"<EMAIL>","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 11:15:21","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:15:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:35","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:15:35","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:15:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:15:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to process assessment job","processingTime":"52640ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:15:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 11:15:35","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:15:38","version":"1.0.0"}
{"candidatesTokenCount":1005,"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"AI response received","responseLength":4334,"service":"analysis-worker","thoughtsTokenCount":2146,"timestamp":"2025-07-17 11:15:39","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 11:15:39","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:15:39","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:39","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:39","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:15:39","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:15:44","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:44","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:44","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:15:44","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:15:54","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:54","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:54","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"delay":20000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:15:54","version":"1.0.0"}
{"delay":20000,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Retry message published successfully","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 11:15:55","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Processing assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 11:15:55","userEmail":"<EMAIL>","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 11:15:55","userEmail":"<EMAIL>","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 11:15:55","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:16:08","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:16:14","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"candidatesTokenCount":783,"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"AI response received","responseLength":3816,"service":"analysis-worker","thoughtsTokenCount":2899,"timestamp":"2025-07-17 11:16:14","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 11:16:14","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem-Solver","service":"analysis-worker","timestamp":"2025-07-17 11:16:14","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:14","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:14","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:16:14","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:16:14","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:14","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:14","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:16:14","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:16:14","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to process assessment job","processingTime":"52595ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:16:14","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:16:14","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem-Solver","service":"analysis-worker","timestamp":"2025-07-17 11:16:19","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:19","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:19","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:16:19","version":"1.0.0"}
{"delay":10000,"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Retry message published successfully","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:16:24","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Processing assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:16:24","userEmail":"<EMAIL>","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 11:16:24","userEmail":"<EMAIL>","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 11:16:24","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem-Solver","service":"analysis-worker","timestamp":"2025-07-17 11:16:29","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:29","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:29","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"delay":20000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:16:29","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:16:38","version":"1.0.0"}
{"candidatesTokenCount":1052,"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"AI response received","responseLength":4525,"service":"analysis-worker","thoughtsTokenCount":2571,"timestamp":"2025-07-17 11:16:42","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 11:16:42","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:16:42","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:42","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:42","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:16:42","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:16:47","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:47","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:47","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:16:47","version":"1.0.0"}
{"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem-Solver","service":"analysis-worker","timestamp":"2025-07-17 11:16:49","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:49","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:49","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:16:49","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:16:49","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:16:49","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to process assessment job","processingTime":"54381ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 11:16:49","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 11:16:49","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:16:57","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:57","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:57","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"delay":20000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:16:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:17:08","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:17:17","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-17 11:17:17","url":"/archive/results","version":"1.0.0"}
{"error":"","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-17 11:17:17","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:17:17","version":"1.0.0"}
{"error":"","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:17:17","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:17:17","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Assessment processing failed: ","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to process assessment job","processingTime":"53210ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:17:17","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 11:17:17","version":"1.0.0"}
{"delay":20000,"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Retry message published successfully","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 11:17:37","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Processing assessment job","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 11:17:37","userEmail":"<EMAIL>","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 11:17:37","userEmail":"<EMAIL>","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 11:17:37","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:17:38","version":"1.0.0"}
{"candidatesTokenCount":952,"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"AI response received","responseLength":4002,"service":"analysis-worker","thoughtsTokenCount":2938,"timestamp":"2025-07-17 11:17:57","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 11:17:57","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Investigator","service":"analysis-worker","timestamp":"2025-07-17 11:17:57","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:17:57","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:17:57","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:17:57","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Investigator","service":"analysis-worker","timestamp":"2025-07-17 11:18:02","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:18:02","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:18:02","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:18:02","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:18:08","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Investigator","service":"analysis-worker","timestamp":"2025-07-17 11:18:12","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:18:12","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:18:12","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"delay":20000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:18:12","version":"1.0.0"}
{"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Investigator","service":"analysis-worker","timestamp":"2025-07-17 11:18:32","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:18:32","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:18:32","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:18:32","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:18:32","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:18:32","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to process assessment job","processingTime":"55164ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 11:18:32","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 11:18:32","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:18:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:19:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:19:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:20:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:20:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:21:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:21:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:22:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:22:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:23:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:23:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:24:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:24:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:25:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:25:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:26:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:26:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:27:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:27:38","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 11:27:53","userEmail":"<EMAIL>","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 11:27:53","userEmail":"<EMAIL>","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 11:27:53","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:28:08","version":"1.0.0"}
{"candidatesTokenCount":1058,"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"AI response received","responseLength":4414,"service":"analysis-worker","thoughtsTokenCount":2195,"timestamp":"2025-07-17 11:28:10","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 11:28:10","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:28:10","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:10","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:10","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:28:10","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:28:15","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:15","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:15","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:28:15","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:28:25","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:25","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:25","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"delay":20000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:28:25","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:28:38","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 11:28:45","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:45","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:45","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:28:45","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:28:45","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:28:45","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to process assessment job","processingTime":"52412ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 11:28:45","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:28:45","version":"1.0.0"}
{"delay":5000,"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Retry message published successfully","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:28:50","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Processing assessment job","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:28:50","userEmail":"<EMAIL>","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 11:28:50","userEmail":"<EMAIL>","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 11:28:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:29:08","version":"1.0.0"}
{"candidatesTokenCount":731,"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"AI response received","responseLength":3611,"service":"analysis-worker","thoughtsTokenCount":2520,"timestamp":"2025-07-17 11:29:09","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 11:29:09","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Investigator","service":"analysis-worker","timestamp":"2025-07-17 11:29:09","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:09","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:09","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:29:09","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Investigator","service":"analysis-worker","timestamp":"2025-07-17 11:29:14","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:14","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:14","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:29:14","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Investigator","service":"analysis-worker","timestamp":"2025-07-17 11:29:24","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:24","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:24","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"delay":20000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 3/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:29:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:29:38","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Investigator","service":"analysis-worker","timestamp":"2025-07-17 11:29:44","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:44","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:44","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:29:44","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:29:44","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:29:44","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to process assessment job","processingTime":"53945ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:29:44","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","maxRetries":3,"message":"Retrying assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:29:44","version":"1.0.0"}
{"delay":10000,"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Retry message published successfully","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:29:54","version":"1.0.0"}
{"isRetryAttempt":true,"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Processing assessment job","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:29:54","userEmail":"<EMAIL>","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 11:29:54","userEmail":"<EMAIL>","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 11:29:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:30:08","version":"1.0.0"}
{"candidatesTokenCount":832,"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"AI response received","responseLength":3826,"service":"analysis-worker","thoughtsTokenCount":2614,"timestamp":"2025-07-17 11:30:13","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 11:30:13","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem-Solver","service":"analysis-worker","timestamp":"2025-07-17 11:30:13","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:30:13","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:30:13","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"delay":5000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 1/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:30:13","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem-Solver","service":"analysis-worker","timestamp":"2025-07-17 11:30:18","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:30:18","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:30:18","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"delay":10000,"error":"Request failed with status code 500","level":"warn","message":"Retry attempt 2/3 for Archive service save","operationName":"Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:30:18","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem-Solver","service":"analysis-worker","timestamp":"2025-07-17 11:30:28","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Analysis result saved successfully","resultId":"f072e96c-2546-4052-ab04-365c5014100d","service":"analysis-worker","status":201,"timestamp":"2025-07-17 11:30:28","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Analysis result saved to Archive Service","resultId":"f072e96c-2546-4052-ab04-365c5014100d","service":"analysis-worker","timestamp":"2025-07-17 11:30:28","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-17 11:30:28","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Analysis completion notification sent","resultId":"f072e96c-2546-4052-ab04-365c5014100d","service":"analysis-worker","timestamp":"2025-07-17 11:30:28","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"info","message":"Assessment job processed successfully","processingTime":"34372ms","resultId":"f072e96c-2546-4052-ab04-365c5014100d","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:30:28","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:30:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:31:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:31:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:32:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:32:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:33:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:33:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:34:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:34:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:35:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:35:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:36:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:36:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:37:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:37:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:38:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:38:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:39:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:39:38","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 11:40:04","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 11:40:04","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 11:40:05","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 11:40:05","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 11:40:05","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 11:40:05","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:40:08","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 11:40:21","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 11:40:21","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 11:40:21","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 11:40:21","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 11:40:21","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 11:40:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:40:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:41:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:41:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:42:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:42:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:43:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:43:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:44:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:44:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:45:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:45:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:46:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:46:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:47:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:47:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:48:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:48:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:49:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:49:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:50:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:50:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:51:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:51:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:52:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:52:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:53:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:53:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:54:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:54:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:55:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:55:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:56:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:56:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:57:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:57:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:58:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:58:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:59:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 11:59:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:00:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:00:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:01:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:01:52","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:02:22","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 12:05:07","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 12:05:07","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 12:05:07","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 12:05:08","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 12:05:08","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 12:05:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:05:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:06:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:06:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:07:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:07:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:08:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:08:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:09:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:09:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:10:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:10:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:11:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:11:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:12:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:12:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:13:08","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 12:13:16","userEmail":"<EMAIL>","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 12:13:16","userEmail":"<EMAIL>","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 12:13:16","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-17 12:13:16","version":"1.0.0"}
{"candidatesTokenCount":958,"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"info","message":"AI response received","responseLength":4066,"service":"analysis-worker","thoughtsTokenCount":2129,"timestamp":"2025-07-17 12:13:33","version":"1.0.0"}
{"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 12:13:33","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Architect","service":"analysis-worker","timestamp":"2025-07-17 12:13:33","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"info","message":"Analysis result saved successfully","resultId":"8326024e-8920-459a-9b81-cf1bfe5fcae7","service":"analysis-worker","status":201,"timestamp":"2025-07-17 12:13:33","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"info","message":"Analysis result saved to Archive Service","resultId":"8326024e-8920-459a-9b81-cf1bfe5fcae7","service":"analysis-worker","timestamp":"2025-07-17 12:13:33","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-17 12:13:33","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"info","message":"Analysis completion notification sent","resultId":"8326024e-8920-459a-9b81-cf1bfe5fcae7","service":"analysis-worker","timestamp":"2025-07-17 12:13:33","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"info","message":"Assessment job processed successfully","processingTime":"16624ms","resultId":"8326024e-8920-459a-9b81-cf1bfe5fcae7","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 12:13:33","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:13:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:14:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:14:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:15:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:15:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:16:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:16:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:17:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:17:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:18:08","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:18:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:19:08","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 12:19:25","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 12:19:25","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 12:19:25","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 12:19:25","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 12:19:25","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 12:19:25","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:19:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:19:55","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:20:08","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 12:22:12","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 12:22:12","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 12:22:12","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 12:22:12","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 12:22:12","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 12:22:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:22:42","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 12:22:48","userEmail":"<EMAIL>","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 12:22:48","userEmail":"<EMAIL>","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 12:22:48","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-17 12:22:48","version":"1.0.0"}
{"candidatesTokenCount":871,"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","level":"info","message":"AI response received","responseLength":3556,"service":"analysis-worker","thoughtsTokenCount":2200,"timestamp":"2025-07-17 12:23:04","version":"1.0.0"}
{"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 12:23:04","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"Strategic Systems Architect","service":"analysis-worker","timestamp":"2025-07-17 12:23:04","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","level":"info","message":"Analysis result saved successfully","resultId":"a268504f-fdf4-4e1f-bac7-f3a4ae40bcd2","service":"analysis-worker","status":201,"timestamp":"2025-07-17 12:23:05","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","level":"info","message":"Analysis result saved to Archive Service","resultId":"a268504f-fdf4-4e1f-bac7-f3a4ae40bcd2","service":"analysis-worker","timestamp":"2025-07-17 12:23:05","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","level":"info","message":"Analysis complete notification sent","resultId":"a268504f-fdf4-4e1f-bac7-f3a4ae40bcd2","service":"analysis-worker","timestamp":"2025-07-17 12:23:05","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","level":"info","message":"Analysis completion notification sent","resultId":"a268504f-fdf4-4e1f-bac7-f3a4ae40bcd2","service":"analysis-worker","timestamp":"2025-07-17 12:23:05","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","level":"info","message":"Assessment job processed successfully","processingTime":"16667ms","resultId":"a268504f-fdf4-4e1f-bac7-f3a4ae40bcd2","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 12:23:05","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:23:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:23:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:24:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:24:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:25:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:25:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:26:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:26:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:27:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:27:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:28:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:28:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:29:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:29:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:30:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:30:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:31:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 12:31:22","userEmail":"<EMAIL>","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 12:31:22","userEmail":"<EMAIL>","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 12:31:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:31:42","version":"1.0.0"}
{"candidatesTokenCount":968,"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","level":"info","message":"AI response received","responseLength":4039,"service":"analysis-worker","thoughtsTokenCount":2990,"timestamp":"2025-07-17 12:31:43","version":"1.0.0"}
{"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 12:31:43","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Innovator","service":"analysis-worker","timestamp":"2025-07-17 12:31:43","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","level":"info","message":"Analysis result saved successfully","resultId":"3307a622-c711-438e-be5a-9e2396119046","service":"analysis-worker","status":201,"timestamp":"2025-07-17 12:31:43","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","level":"info","message":"Analysis result saved to Archive Service","resultId":"3307a622-c711-438e-be5a-9e2396119046","service":"analysis-worker","timestamp":"2025-07-17 12:31:43","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","level":"info","message":"Analysis complete notification sent","resultId":"3307a622-c711-438e-be5a-9e2396119046","service":"analysis-worker","timestamp":"2025-07-17 12:31:43","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","level":"info","message":"Analysis completion notification sent","resultId":"3307a622-c711-438e-be5a-9e2396119046","service":"analysis-worker","timestamp":"2025-07-17 12:31:43","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","level":"info","message":"Assessment job processed successfully","processingTime":"20787ms","resultId":"3307a622-c711-438e-be5a-9e2396119046","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 12:31:43","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 12:32:12","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 18:15:26","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 18:15:26","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 18:15:26","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 18:15:26","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 18:15:26","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 18:15:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:15:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:16:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:16:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:17:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:17:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:18:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:18:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:19:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:19:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:20:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:20:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:21:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:21:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:22:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:22:56","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 18:23:12","userEmail":"<EMAIL>","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 18:23:12","userEmail":"<EMAIL>","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 18:23:12","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-17 18:23:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:23:26","version":"1.0.0"}
{"candidatesTokenCount":929,"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","level":"info","message":"AI response received","responseLength":3978,"service":"analysis-worker","thoughtsTokenCount":3024,"timestamp":"2025-07-17 18:23:33","version":"1.0.0"}
{"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 18:23:33","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Innovator","service":"analysis-worker","timestamp":"2025-07-17 18:23:33","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","level":"info","message":"Analysis result saved successfully","resultId":"aa5dec84-37b4-4ff6-9b31-2b7efe6f4df9","service":"analysis-worker","status":201,"timestamp":"2025-07-17 18:23:33","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","level":"info","message":"Analysis result saved to Archive Service","resultId":"aa5dec84-37b4-4ff6-9b31-2b7efe6f4df9","service":"analysis-worker","timestamp":"2025-07-17 18:23:33","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","level":"info","message":"Analysis complete notification sent","resultId":"aa5dec84-37b4-4ff6-9b31-2b7efe6f4df9","service":"analysis-worker","timestamp":"2025-07-17 18:23:34","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","level":"info","message":"Analysis completion notification sent","resultId":"aa5dec84-37b4-4ff6-9b31-2b7efe6f4df9","service":"analysis-worker","timestamp":"2025-07-17 18:23:34","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","level":"info","message":"Assessment job processed successfully","processingTime":"21950ms","resultId":"aa5dec84-37b4-4ff6-9b31-2b7efe6f4df9","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 18:23:34","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:23:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:24:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:24:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:25:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:25:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:26:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 18:26:39","userEmail":"<EMAIL>","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 18:26:39","userEmail":"<EMAIL>","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 18:26:39","version":"1.0.0"}
{"candidatesTokenCount":913,"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","level":"info","message":"AI response received","responseLength":3848,"service":"analysis-worker","thoughtsTokenCount":1605,"timestamp":"2025-07-17 18:26:54","version":"1.0.0"}
{"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 18:26:54","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-17 18:26:54","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","level":"info","message":"Analysis result saved successfully","resultId":"56b54c5d-32d5-4ab6-b3fe-50dbf2d007f6","service":"analysis-worker","status":201,"timestamp":"2025-07-17 18:26:54","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","level":"info","message":"Analysis result saved to Archive Service","resultId":"56b54c5d-32d5-4ab6-b3fe-50dbf2d007f6","service":"analysis-worker","timestamp":"2025-07-17 18:26:54","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","level":"info","message":"Analysis complete notification sent","resultId":"56b54c5d-32d5-4ab6-b3fe-50dbf2d007f6","service":"analysis-worker","timestamp":"2025-07-17 18:26:54","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","level":"info","message":"Analysis completion notification sent","resultId":"56b54c5d-32d5-4ab6-b3fe-50dbf2d007f6","service":"analysis-worker","timestamp":"2025-07-17 18:26:54","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","level":"info","message":"Assessment job processed successfully","processingTime":"15090ms","resultId":"56b54c5d-32d5-4ab6-b3fe-50dbf2d007f6","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 18:26:54","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:26:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:27:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:27:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:28:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:28:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:29:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:29:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:30:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:30:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:31:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:31:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:32:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:32:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:33:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:33:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:34:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:34:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:35:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:35:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:36:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:36:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:37:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:37:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:38:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:38:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:39:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:39:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:40:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:40:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:41:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:41:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:42:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:42:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:43:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:43:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:44:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:44:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:45:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:45:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:46:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:46:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:47:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:47:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:48:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:48:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:49:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:49:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:50:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:50:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:51:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:51:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:52:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:52:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:53:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:53:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:54:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:54:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:55:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:55:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:56:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:56:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:57:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:57:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:58:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:58:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:59:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 18:59:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:00:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:00:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:01:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:01:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:02:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:02:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:03:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:03:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:04:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:04:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:05:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:05:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:06:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:06:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:07:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:07:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:08:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:08:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:09:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:09:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:10:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:10:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:11:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:11:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:12:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:12:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:13:26","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:13:56","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:14:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:14:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:15:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:15:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:16:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:16:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:17:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:17:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:18:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:18:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:19:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:19:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:20:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:20:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:21:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:21:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:22:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:22:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:23:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:23:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:24:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:24:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:25:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:25:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:26:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:26:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:27:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:27:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:28:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:28:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:29:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:29:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:30:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:30:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:31:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 19:31:29","userEmail":"<EMAIL>","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 19:31:29","userEmail":"<EMAIL>","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 19:31:29","version":"1.0.0"}
{"candidatesTokenCount":1047,"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","level":"info","message":"AI response received","responseLength":4450,"service":"analysis-worker","thoughtsTokenCount":2121,"timestamp":"2025-07-17 19:31:46","version":"1.0.0"}
{"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 19:31:46","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem-Solver","service":"analysis-worker","timestamp":"2025-07-17 19:31:46","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","level":"info","message":"Analysis result saved successfully","resultId":"b5de0be2-388c-419b-a234-9afadf21f727","service":"analysis-worker","status":201,"timestamp":"2025-07-17 19:31:47","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","level":"info","message":"Analysis result saved to Archive Service","resultId":"b5de0be2-388c-419b-a234-9afadf21f727","service":"analysis-worker","timestamp":"2025-07-17 19:31:47","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","level":"info","message":"Analysis complete notification sent","resultId":"b5de0be2-388c-419b-a234-9afadf21f727","service":"analysis-worker","timestamp":"2025-07-17 19:31:47","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","level":"info","message":"Analysis completion notification sent","resultId":"b5de0be2-388c-419b-a234-9afadf21f727","service":"analysis-worker","timestamp":"2025-07-17 19:31:47","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","level":"info","message":"Assessment job processed successfully","processingTime":"17949ms","resultId":"b5de0be2-388c-419b-a234-9afadf21f727","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 19:31:47","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:31:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:32:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:32:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:33:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:33:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:34:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 19:34:32","userEmail":"<EMAIL>","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 19:34:32","userEmail":"<EMAIL>","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 19:34:32","version":"1.0.0"}
{"candidatesTokenCount":1094,"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","level":"info","message":"AI response received","responseLength":4635,"service":"analysis-worker","thoughtsTokenCount":2405,"timestamp":"2025-07-17 19:34:51","version":"1.0.0"}
{"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 19:34:51","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Innovator-Analyst","service":"analysis-worker","timestamp":"2025-07-17 19:34:51","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","level":"info","message":"Analysis result saved successfully","resultId":"1566a067-09e2-4973-a382-c8dc4a973299","service":"analysis-worker","status":201,"timestamp":"2025-07-17 19:34:51","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","level":"info","message":"Analysis result saved to Archive Service","resultId":"1566a067-09e2-4973-a382-c8dc4a973299","service":"analysis-worker","timestamp":"2025-07-17 19:34:51","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","level":"info","message":"Analysis complete notification sent","resultId":"1566a067-09e2-4973-a382-c8dc4a973299","service":"analysis-worker","timestamp":"2025-07-17 19:34:51","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","level":"info","message":"Analysis completion notification sent","resultId":"1566a067-09e2-4973-a382-c8dc4a973299","service":"analysis-worker","timestamp":"2025-07-17 19:34:51","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","level":"info","message":"Assessment job processed successfully","processingTime":"19215ms","resultId":"1566a067-09e2-4973-a382-c8dc4a973299","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 19:34:51","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:34:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:35:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:35:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:36:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:36:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:37:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:37:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:38:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:38:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:39:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:39:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:40:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:40:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:41:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:41:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:42:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:42:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:43:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:43:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:44:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:44:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:45:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:45:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:46:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:46:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:47:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:47:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:48:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:48:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:49:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:49:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:50:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:50:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:51:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:51:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:52:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:52:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:53:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:53:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:54:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:54:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:55:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:55:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:56:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:56:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:57:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:57:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:58:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:58:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:59:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 19:59:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:00:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:00:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:01:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:01:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:02:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:02:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:03:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:03:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:04:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:04:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:05:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:05:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:06:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:06:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:07:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:07:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:08:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:08:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:09:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:09:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:10:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:10:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:11:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:11:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:12:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:12:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:13:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:13:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:14:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:14:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:15:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:15:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:16:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:16:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:17:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:17:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:18:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:18:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:19:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:19:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:20:27","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:20:57","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:21:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:21:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:22:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:22:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:23:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:23:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:24:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:24:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:25:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:25:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:26:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:26:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:27:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 20:27:31","userEmail":"<EMAIL>","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 20:27:31","userEmail":"<EMAIL>","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 20:27:31","version":"1.0.0"}
{"candidatesTokenCount":924,"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","level":"info","message":"AI response received","responseLength":3839,"service":"analysis-worker","thoughtsTokenCount":2555,"timestamp":"2025-07-17 20:27:53","version":"1.0.0"}
{"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 20:27:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem-Solver","service":"analysis-worker","timestamp":"2025-07-17 20:27:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","level":"info","message":"Analysis result saved successfully","resultId":"9a115137-36b0-4e9f-a9c4-805ff0b14110","service":"analysis-worker","status":201,"timestamp":"2025-07-17 20:27:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","level":"info","message":"Analysis result saved to Archive Service","resultId":"9a115137-36b0-4e9f-a9c4-805ff0b14110","service":"analysis-worker","timestamp":"2025-07-17 20:27:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","level":"info","message":"Analysis complete notification sent","resultId":"9a115137-36b0-4e9f-a9c4-805ff0b14110","service":"analysis-worker","timestamp":"2025-07-17 20:27:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","level":"info","message":"Analysis completion notification sent","resultId":"9a115137-36b0-4e9f-a9c4-805ff0b14110","service":"analysis-worker","timestamp":"2025-07-17 20:27:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","level":"info","message":"Assessment job processed successfully","processingTime":"21654ms","resultId":"9a115137-36b0-4e9f-a9c4-805ff0b14110","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 20:27:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:27:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:28:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 20:28:33","userEmail":"<EMAIL>","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 20:28:33","userEmail":"<EMAIL>","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 20:28:33","version":"1.0.0"}
{"candidatesTokenCount":892,"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","level":"info","message":"AI response received","responseLength":3784,"service":"analysis-worker","thoughtsTokenCount":2279,"timestamp":"2025-07-17 20:28:53","version":"1.0.0"}
{"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 20:28:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Innovator","service":"analysis-worker","timestamp":"2025-07-17 20:28:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","level":"info","message":"Analysis result saved successfully","resultId":"bde99b66-2894-467f-bb1d-4cae156e3dd8","service":"analysis-worker","status":201,"timestamp":"2025-07-17 20:28:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","level":"info","message":"Analysis result saved to Archive Service","resultId":"bde99b66-2894-467f-bb1d-4cae156e3dd8","service":"analysis-worker","timestamp":"2025-07-17 20:28:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","level":"info","message":"Analysis complete notification sent","resultId":"bde99b66-2894-467f-bb1d-4cae156e3dd8","service":"analysis-worker","timestamp":"2025-07-17 20:28:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","level":"info","message":"Analysis completion notification sent","resultId":"bde99b66-2894-467f-bb1d-4cae156e3dd8","service":"analysis-worker","timestamp":"2025-07-17 20:28:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","level":"info","message":"Assessment job processed successfully","processingTime":"20407ms","resultId":"bde99b66-2894-467f-bb1d-4cae156e3dd8","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 20:28:53","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:28:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:29:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:29:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:30:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:30:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:31:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:31:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:32:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:32:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:33:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 20:33:46","userEmail":"<EMAIL>","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 20:33:46","userEmail":"<EMAIL>","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 20:33:46","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:33:58","version":"1.0.0"}
{"candidatesTokenCount":1021,"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","level":"info","message":"AI response received","responseLength":4321,"service":"analysis-worker","thoughtsTokenCount":2230,"timestamp":"2025-07-17 20:34:05","version":"1.0.0"}
{"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 20:34:05","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Innovator","service":"analysis-worker","timestamp":"2025-07-17 20:34:05","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","level":"info","message":"Analysis result saved successfully","resultId":"47dc3ba5-032f-4d28-9a5d-fb2c6d97429b","service":"analysis-worker","status":201,"timestamp":"2025-07-17 20:34:05","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","level":"info","message":"Analysis result saved to Archive Service","resultId":"47dc3ba5-032f-4d28-9a5d-fb2c6d97429b","service":"analysis-worker","timestamp":"2025-07-17 20:34:05","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","level":"info","message":"Analysis complete notification sent","resultId":"47dc3ba5-032f-4d28-9a5d-fb2c6d97429b","service":"analysis-worker","timestamp":"2025-07-17 20:34:05","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","level":"info","message":"Analysis completion notification sent","resultId":"47dc3ba5-032f-4d28-9a5d-fb2c6d97429b","service":"analysis-worker","timestamp":"2025-07-17 20:34:05","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","level":"info","message":"Assessment job processed successfully","processingTime":"18884ms","resultId":"47dc3ba5-032f-4d28-9a5d-fb2c6d97429b","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 20:34:05","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:34:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:34:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:35:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:35:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:36:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:36:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:37:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:37:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:38:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:38:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:39:28","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 20:46:25","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 20:46:25","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 20:46:25","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 20:46:25","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 20:46:25","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 20:46:25","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:46:55","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 20:46:59","userEmail":"<EMAIL>","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 20:46:59","userEmail":"<EMAIL>","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 20:46:59","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-17 20:46:59","version":"1.0.0"}
{"candidatesTokenCount":1009,"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","level":"info","message":"AI response received","responseLength":4190,"service":"analysis-worker","thoughtsTokenCount":2582,"timestamp":"2025-07-17 20:47:20","version":"1.0.0"}
{"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 20:47:20","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Innovator","service":"analysis-worker","timestamp":"2025-07-17 20:47:20","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","level":"info","message":"Analysis result saved successfully","resultId":"da7acc61-e242-405b-b189-fb02bf80b945","service":"analysis-worker","status":201,"timestamp":"2025-07-17 20:47:20","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","level":"info","message":"Analysis result saved to Archive Service","resultId":"da7acc61-e242-405b-b189-fb02bf80b945","service":"analysis-worker","timestamp":"2025-07-17 20:47:20","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","level":"info","message":"Analysis complete notification sent","resultId":"da7acc61-e242-405b-b189-fb02bf80b945","service":"analysis-worker","timestamp":"2025-07-17 20:47:20","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","level":"info","message":"Analysis completion notification sent","resultId":"da7acc61-e242-405b-b189-fb02bf80b945","service":"analysis-worker","timestamp":"2025-07-17 20:47:20","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","level":"info","message":"Assessment job processed successfully","processingTime":"21084ms","resultId":"da7acc61-e242-405b-b189-fb02bf80b945","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 20:47:20","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:47:25","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:47:55","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-17 20:55:51","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-17 20:55:51","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 20:55:51","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-17 20:55:51","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-17 20:55:51","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-17 20:55:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:56:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:56:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:57:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:57:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:58:21","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"fafd7290-7212-4066-b274-2da3388938ba","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 20:58:39","userEmail":"<EMAIL>","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"fafd7290-7212-4066-b274-2da3388938ba","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 20:58:39","userEmail":"<EMAIL>","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"fafd7290-7212-4066-b274-2da3388938ba","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 20:58:39","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-17 20:58:39","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:58:51","version":"1.0.0"}
{"candidatesTokenCount":1069,"jobId":"fafd7290-7212-4066-b274-2da3388938ba","level":"info","message":"AI response received","responseLength":4546,"service":"analysis-worker","thoughtsTokenCount":2715,"timestamp":"2025-07-17 20:59:01","version":"1.0.0"}
{"jobId":"fafd7290-7212-4066-b274-2da3388938ba","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 20:59:01","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"fafd7290-7212-4066-b274-2da3388938ba","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem Solver","service":"analysis-worker","timestamp":"2025-07-17 20:59:01","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"fafd7290-7212-4066-b274-2da3388938ba","level":"info","message":"Analysis result saved successfully","resultId":"9f70db1e-9f41-4572-8c41-03a09f400144","service":"analysis-worker","status":201,"timestamp":"2025-07-17 20:59:01","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"fafd7290-7212-4066-b274-2da3388938ba","level":"info","message":"Analysis result saved to Archive Service","resultId":"9f70db1e-9f41-4572-8c41-03a09f400144","service":"analysis-worker","timestamp":"2025-07-17 20:59:01","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"fafd7290-7212-4066-b274-2da3388938ba","level":"info","message":"Analysis complete notification sent","resultId":"9f70db1e-9f41-4572-8c41-03a09f400144","service":"analysis-worker","timestamp":"2025-07-17 20:59:01","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"fafd7290-7212-4066-b274-2da3388938ba","level":"info","message":"Analysis completion notification sent","resultId":"9f70db1e-9f41-4572-8c41-03a09f400144","service":"analysis-worker","timestamp":"2025-07-17 20:59:01","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"fafd7290-7212-4066-b274-2da3388938ba","level":"info","message":"Assessment job processed successfully","processingTime":"21487ms","resultId":"9f70db1e-9f41-4572-8c41-03a09f400144","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 20:59:01","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:59:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 20:59:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:00:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:00:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:01:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:01:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:02:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:02:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:03:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:03:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:04:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:04:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:05:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:05:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:06:21","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 21:06:48","userEmail":"<EMAIL>","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 21:06:48","userEmail":"<EMAIL>","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 21:06:48","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:06:51","version":"1.0.0"}
{"candidatesTokenCount":940,"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","level":"info","message":"AI response received","responseLength":3906,"service":"analysis-worker","thoughtsTokenCount":2186,"timestamp":"2025-07-17 21:07:06","version":"1.0.0"}
{"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 21:07:06","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Architect","service":"analysis-worker","timestamp":"2025-07-17 21:07:06","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","level":"info","message":"Analysis result saved successfully","resultId":"b3bc9708-3f51-43f3-9ba5-30ad50153870","service":"analysis-worker","status":201,"timestamp":"2025-07-17 21:07:06","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","level":"info","message":"Analysis result saved to Archive Service","resultId":"b3bc9708-3f51-43f3-9ba5-30ad50153870","service":"analysis-worker","timestamp":"2025-07-17 21:07:06","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","level":"info","message":"Analysis complete notification sent","resultId":"b3bc9708-3f51-43f3-9ba5-30ad50153870","service":"analysis-worker","timestamp":"2025-07-17 21:07:06","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","level":"info","message":"Analysis completion notification sent","resultId":"b3bc9708-3f51-43f3-9ba5-30ad50153870","service":"analysis-worker","timestamp":"2025-07-17 21:07:06","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","level":"info","message":"Assessment job processed successfully","processingTime":"18129ms","resultId":"b3bc9708-3f51-43f3-9ba5-30ad50153870","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 21:07:06","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 21:07:18","userEmail":"<EMAIL>","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-17 21:07:18","userEmail":"<EMAIL>","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-17 21:07:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:07:21","version":"1.0.0"}
{"candidatesTokenCount":1015,"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","level":"info","message":"AI response received","responseLength":4384,"service":"analysis-worker","thoughtsTokenCount":2468,"timestamp":"2025-07-17 21:07:39","version":"1.0.0"}
{"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-17 21:07:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Principled Innovator","service":"analysis-worker","timestamp":"2025-07-17 21:07:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","level":"info","message":"Analysis result saved successfully","resultId":"b9f98f4e-aafc-4a2b-87cb-f1f8b08b24dd","service":"analysis-worker","status":201,"timestamp":"2025-07-17 21:07:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","level":"info","message":"Analysis result saved to Archive Service","resultId":"b9f98f4e-aafc-4a2b-87cb-f1f8b08b24dd","service":"analysis-worker","timestamp":"2025-07-17 21:07:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","level":"info","message":"Analysis complete notification sent","resultId":"b9f98f4e-aafc-4a2b-87cb-f1f8b08b24dd","service":"analysis-worker","timestamp":"2025-07-17 21:07:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","level":"info","message":"Analysis completion notification sent","resultId":"b9f98f4e-aafc-4a2b-87cb-f1f8b08b24dd","service":"analysis-worker","timestamp":"2025-07-17 21:07:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","level":"info","message":"Assessment job processed successfully","processingTime":"20459ms","resultId":"b9f98f4e-aafc-4a2b-87cb-f1f8b08b24dd","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 21:07:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:07:51","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:08:21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-17 21:08:51","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 03:53:11","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 03:53:11","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 03:53:11","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-18 03:53:11","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 03:53:11","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-18 03:53:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:53:41","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 03:53:50","userEmail":"<EMAIL>","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 03:53:50","userEmail":"<EMAIL>","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 03:53:50","version":"1.0.0"}
{"level":"info","message":"Google Generative AI initialized successfully","model":"gemini-2.5-flash","service":"analysis-worker","temperature":0.7,"timestamp":"2025-07-18 03:53:50","version":"1.0.0"}
{"candidatesTokenCount":911,"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","level":"info","message":"AI response received","responseLength":4069,"service":"analysis-worker","thoughtsTokenCount":2494,"timestamp":"2025-07-18 03:54:07","version":"1.0.0"}
{"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 03:54:07","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Investigator","service":"analysis-worker","timestamp":"2025-07-18 03:54:07","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","level":"info","message":"Analysis result saved successfully","resultId":"d6cb4aec-7620-430f-8400-d6a5a46ea8b4","service":"analysis-worker","status":201,"timestamp":"2025-07-18 03:54:07","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","level":"info","message":"Analysis result saved to Archive Service","resultId":"d6cb4aec-7620-430f-8400-d6a5a46ea8b4","service":"analysis-worker","timestamp":"2025-07-18 03:54:07","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","level":"info","message":"Analysis complete notification sent","resultId":"d6cb4aec-7620-430f-8400-d6a5a46ea8b4","service":"analysis-worker","timestamp":"2025-07-18 03:54:07","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","level":"info","message":"Analysis completion notification sent","resultId":"d6cb4aec-7620-430f-8400-d6a5a46ea8b4","service":"analysis-worker","timestamp":"2025-07-18 03:54:07","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","level":"info","message":"Assessment job processed successfully","processingTime":"17125ms","resultId":"d6cb4aec-7620-430f-8400-d6a5a46ea8b4","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 03:54:07","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:54:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:54:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:55:11","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 03:55:38","userEmail":"<EMAIL>","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 03:55:38","userEmail":"<EMAIL>","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 03:55:38","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:55:41","version":"1.0.0"}
{"candidatesTokenCount":877,"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","level":"info","message":"AI response received","responseLength":3617,"service":"analysis-worker","thoughtsTokenCount":2846,"timestamp":"2025-07-18 03:55:58","version":"1.0.0"}
{"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 03:55:58","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Strategic Problem Solver","service":"analysis-worker","timestamp":"2025-07-18 03:55:58","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","level":"info","message":"Analysis result saved successfully","resultId":"3ffe0b09-a497-48fc-9edd-b7889bb63aa1","service":"analysis-worker","status":201,"timestamp":"2025-07-18 03:55:58","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","level":"info","message":"Analysis result saved to Archive Service","resultId":"3ffe0b09-a497-48fc-9edd-b7889bb63aa1","service":"analysis-worker","timestamp":"2025-07-18 03:55:58","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","level":"info","message":"Analysis complete notification sent","resultId":"3ffe0b09-a497-48fc-9edd-b7889bb63aa1","service":"analysis-worker","timestamp":"2025-07-18 03:55:58","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","level":"info","message":"Analysis completion notification sent","resultId":"3ffe0b09-a497-48fc-9edd-b7889bb63aa1","service":"analysis-worker","timestamp":"2025-07-18 03:55:58","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","level":"info","message":"Assessment job processed successfully","processingTime":"19613ms","resultId":"3ffe0b09-a497-48fc-9edd-b7889bb63aa1","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 03:55:58","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:56:11","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 03:56:19","userEmail":"<EMAIL>","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-18 03:56:19","userEmail":"<EMAIL>","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-18 03:56:19","version":"1.0.0"}
{"candidatesTokenCount":730,"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","level":"info","message":"AI response received","responseLength":3478,"service":"analysis-worker","thoughtsTokenCount":2450,"timestamp":"2025-07-18 03:56:34","version":"1.0.0"}
{"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-18 03:56:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Analytical Innovator","service":"analysis-worker","timestamp":"2025-07-18 03:56:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","level":"info","message":"Analysis result saved successfully","resultId":"64870042-696a-4c01-89a7-7001020efaaa","service":"analysis-worker","status":201,"timestamp":"2025-07-18 03:56:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","level":"info","message":"Analysis result saved to Archive Service","resultId":"64870042-696a-4c01-89a7-7001020efaaa","service":"analysis-worker","timestamp":"2025-07-18 03:56:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","level":"info","message":"Analysis complete notification sent","resultId":"64870042-696a-4c01-89a7-7001020efaaa","service":"analysis-worker","timestamp":"2025-07-18 03:56:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","level":"info","message":"Analysis completion notification sent","resultId":"64870042-696a-4c01-89a7-7001020efaaa","service":"analysis-worker","timestamp":"2025-07-18 03:56:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","level":"info","message":"Assessment job processed successfully","processingTime":"15006ms","resultId":"64870042-696a-4c01-89a7-7001020efaaa","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-18 03:56:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:56:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:57:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:57:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:58:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:58:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:59:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 03:59:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:00:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:00:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:01:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:01:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:02:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:02:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:03:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:03:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:04:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:04:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:05:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:05:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:06:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:06:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:07:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:07:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:08:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:08:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:09:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:09:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:10:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:10:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:11:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:11:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:12:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:12:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:13:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:13:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:14:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:14:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:15:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:15:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:16:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:16:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:17:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:17:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:18:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:18:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:19:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:19:41","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:20:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:20:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:21:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:21:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:22:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:22:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:23:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 04:23:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 05:30:00","version":"1.0.0","workerConcurrency":"3"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 05:30:00","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 05:30:00","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-18 05:30:00","version":"1.0.0"}
{"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 05:30:00","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-18 05:30:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:30:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:31:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:31:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:32:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:32:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:33:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:33:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:34:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:34:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:35:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:35:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:36:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:36:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:37:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:37:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:38:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:38:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:39:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:39:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:40:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:40:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:41:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:41:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:42:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:42:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:43:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:43:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:44:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:44:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:45:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:45:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:46:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:46:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:47:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:47:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 05:48:00","version":"1.0.0"}
