require('dotenv').config();
const sequelize = require('../src/config/database');
const bcrypt = require('bcrypt');

async function testAdminLogin() {
  try {
    console.log('🔍 Testing admin login...');
    
    // Get the superadmin user
    const [adminResults] = await sequelize.query(`
      SELECT id, username, email, password_hash, role, is_active 
      FROM auth.admins 
      WHERE username = 'superadmin'
    `);
    
    if (adminResults.length === 0) {
      console.log('❌ Superadmin user not found');
      return;
    }
    
    const admin = adminResults[0];
    console.log('✅ Found admin user:', {
      id: admin.id,
      username: admin.username,
      email: admin.email,
      role: admin.role,
      is_active: admin.is_active
    });
    
    // Test password comparison
    const testPassword = 'admin123';
    console.log(`\n🔐 Testing password: "${testPassword}"`);
    console.log(`🔐 Stored hash: ${admin.password_hash}`);
    
    const isPasswordValid = await bcrypt.compare(testPassword, admin.password_hash);
    console.log(`🔐 Password valid: ${isPasswordValid}`);
    
    if (!isPasswordValid) {
      console.log('\n❌ Password does not match! Let\'s create a new hash...');
      
      // Create new hash for 'admin123'
      const newHash = await bcrypt.hash(testPassword, 12);
      console.log(`🔐 New hash for "${testPassword}": ${newHash}`);
      
      // Update the password in database
      await sequelize.query(`
        UPDATE auth.admins 
        SET password_hash = :newHash, updated_at = CURRENT_TIMESTAMP
        WHERE username = 'superadmin'
      `, {
        replacements: { newHash }
      });
      
      console.log('✅ Password updated in database');
      
      // Test again
      const isNewPasswordValid = await bcrypt.compare(testPassword, newHash);
      console.log(`🔐 New password valid: ${isNewPasswordValid}`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Run the script
if (require.main === module) {
  testAdminLogin()
    .then(() => {
      console.log('\n🎉 Admin login test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Admin login test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testAdminLogin };
