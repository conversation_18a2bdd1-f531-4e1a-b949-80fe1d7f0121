const bcrypt = require('bcrypt');
const { Admin } = require('../models');
const { generateToken } = require('../utils/jwt');
const { validatePassword } = require('../utils/validation');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Hash password using bcrypt
 * @param {string} password - Plain text password
 * @returns {Promise<string>} Hashed password
 */
const hashPassword = async (password) => {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

/**
 * Compare password with hash
 * @param {string} password - Plain text password
 * @param {string} hash - Hashed password
 * @returns {Promise<boolean>} Password match result
 */
const comparePassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};

/**
 * Register a new admin
 * @param {Object} adminData - Admin registration data
 * @returns {Promise<Object>} Admin and token
 */
const registerAdmin = async (adminData) => {
  const { username, email, password, full_name, role = 'admin' } = adminData;

  try {
    // Validate password strength
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
    }

    // Check if admin already exists
    const existingAdmin = await Admin.findOne({
      where: {
        [Op.or]: [
          { email },
          { username }
        ]
      }
    });
    
    if (existingAdmin) {
      if (existingAdmin.email === email) {
        throw new Error('Email already exists');
      }
      if (existingAdmin.username === username) {
        throw new Error('Username already exists');
      }
    }

    // Hash password
    const password_hash = await hashPassword(password);

    // Create admin
    const admin = await Admin.create({
      username,
      email,
      password_hash,
      full_name,
      role,
      is_active: true
    });

    // Generate JWT token
    const token = generateToken(admin, 'admin');

    logger.info('Admin registered successfully', {
      adminId: admin.id,
      username: admin.username,
      email: admin.email,
      role: admin.role
    });

    return {
      admin: admin.toJSON(),
      token
    };
  } catch (error) {
    logger.error('Admin registration failed', {
      error: error.message,
      username,
      email
    });
    throw error;
  }
};

/**
 * Authenticate admin login
 * @param {Object} credentials - Login credentials
 * @returns {Promise<Object>} Admin and token
 */
const loginAdmin = async (credentials) => {
  const { username, password } = credentials;

  try {
    // Find admin by username or email
    const admin = await Admin.findOne({
      where: {
        [Op.or]: [
          { username },
          { email: username } // Allow login with email
        ],
        is_active: true
      }
    });
    
    if (!admin) {
      throw new Error('Invalid username/email or password');
    }

    // Compare password
    const isPasswordValid = await comparePassword(password, admin.password_hash);
    if (!isPasswordValid) {
      throw new Error('Invalid username/email or password');
    }

    // Update last login
    await admin.update({ last_login: new Date() });

    // Generate JWT token
    const token = generateToken(admin, 'admin');

    logger.info('Admin login successful', {
      adminId: admin.id,
      username: admin.username,
      email: admin.email,
      role: admin.role
    });

    return {
      admin: admin.toJSON(),
      token
    };
  } catch (error) {
    logger.error('Admin login failed', {
      error: error.message,
      username
    });
    throw error;
  }
};

/**
 * Change admin password
 * @param {string} adminId - Admin ID
 * @param {string} currentPassword - Current password
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} Success result
 */
const changeAdminPassword = async (adminId, currentPassword, newPassword) => {
  try {
    // Find admin
    const admin = await Admin.findByPk(adminId);
    if (!admin) {
      throw new Error('Admin not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await comparePassword(currentPassword, admin.password_hash);
    if (!isCurrentPasswordValid) {
      throw new Error('Current password is incorrect');
    }

    // Validate new password
    const passwordValidation = validatePassword(newPassword);
    if (!passwordValidation.isValid) {
      throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
    }

    // Hash new password
    const newPasswordHash = await hashPassword(newPassword);

    // Update password
    await admin.update({ password_hash: newPasswordHash });

    logger.info('Admin password changed successfully', {
      adminId: admin.id,
      username: admin.username
    });

    return {
      success: true,
      message: 'Password changed successfully'
    };
  } catch (error) {
    logger.error('Admin password change failed', {
      error: error.message,
      adminId
    });
    throw error;
  }
};

/**
 * Get admin by ID
 * @param {string} adminId - Admin ID
 * @returns {Promise<Object>} Admin data
 */
const getAdminById = async (adminId) => {
  try {
    const admin = await Admin.findByPk(adminId);
    if (!admin) {
      throw new Error('Admin not found');
    }
    return admin.toJSON();
  } catch (error) {
    logger.error('Get admin by ID failed', {
      error: error.message,
      adminId
    });
    throw error;
  }
};

/**
 * Update admin profile
 * @param {string} adminId - Admin ID
 * @param {Object} updateData - Update data
 * @returns {Promise<Object>} Updated admin data
 */
const updateAdminProfile = async (adminId, updateData) => {
  try {
    const admin = await Admin.findByPk(adminId);
    if (!admin) {
      throw new Error('Admin not found');
    }

    // Only allow certain fields to be updated
    const allowedFields = ['full_name', 'email'];
    const filteredData = {};
    
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    });

    // Check if email is being changed and if it's unique
    if (filteredData.email && filteredData.email !== admin.email) {
      const existingAdmin = await Admin.findOne({
        where: {
          email: filteredData.email,
          id: { [Op.ne]: adminId }
        }
      });
      
      if (existingAdmin) {
        throw new Error('Email already exists');
      }
    }

    await admin.update(filteredData);

    logger.info('Admin profile updated successfully', {
      adminId: admin.id,
      username: admin.username,
      updatedFields: Object.keys(filteredData)
    });

    return admin.toJSON();
  } catch (error) {
    logger.error('Admin profile update failed', {
      error: error.message,
      adminId
    });
    throw error;
  }
};

module.exports = {
  registerAdmin,
  loginAdmin,
  changeAdminPassword,
  getAdminById,
  updateAdminProfile
};
