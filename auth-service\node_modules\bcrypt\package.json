{"name": "bcrypt", "description": "A bcrypt library for NodeJS.", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "main": "./bcrypt", "version": "6.0.0", "author": "<PERSON> (https://github.com/ncb000gt)", "engines": {"node": ">= 18"}, "repository": {"type": "git", "url": "https://github.com/kelektiv/node.bcrypt.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/kelektiv/node.bcrypt.js/issues"}, "scripts": {"test": "jest", "install": "node-gyp-build", "build": "prebuildify --napi --tag-libc --strip"}, "dependencies": {"node-addon-api": "^8.3.0", "node-gyp-build": "^4.8.4"}, "devDependencies": {"jest": "^29.7.0", "prebuildify": "^6.0.1"}, "contributors": ["<PERSON> <<EMAIL>> (https://github.com/Shadowfiend)", "<PERSON> <<EMAIL>> (https://github.com/thegoleffect)", "<PERSON> <<EMAIL>> (https://github.com/dtrejo)", "<PERSON> <<EMAIL>> (https://github.com/pixelglow)", "NewITFarmer.com <> (https://github.com/newitfarmer)", "<PERSON> <<EMAIL>> (https://github.com/alfredwesterveld)", "<PERSON>-Roy <<EMAIL>> (https://github.com/vincentcr)", "<PERSON> <<EMAIL>> (https://github.com/lloyd)", "<PERSON>htylman <<EMAIL>> (https://github.com/shtylman)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/vadimg)", "<PERSON> <> (https://github.com/bnoordhuis)", "<PERSON> <<EMAIL>> (https://github.com/tootallnate)", "<PERSON> <<EMAIL>> (https://github.com/seanmonstar)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/weareu)", "Amito<PERSON> Swain <PERSON> <<EMAIL>> (https://github.com/Agathver)", "<PERSON> <<EMAIL>> (https://github.com/crutchcorn)", "<PERSON> <<EMAIL>> (https://github.com/NickNaso)"], "binary": {"module_name": "bcrypt_lib"}}