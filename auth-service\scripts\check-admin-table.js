require('dotenv').config();
const sequelize = require('../src/config/database');
const bcrypt = require('bcrypt');

async function checkAndCreateAdminTable() {
  try {
    console.log('🔍 Checking admin table...');
    
    // Check if admin table exists
    const [results] = await sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'auth' AND table_name = 'admins'
    `);
    
    if (results.length === 0) {
      console.log('❌ Admin table does not exist. Creating...');
      
      // Create admin table
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS auth.admins (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          username VARCHAR(100) UNIQUE NOT NULL,
          email VARCHAR(255) UNIQUE NOT NULL,
          password_hash VARCHAR(255) NOT NULL,
          full_name <PERSON><PERSON><PERSON><PERSON>(255),
          role VARCHAR(50) NOT NULL DEFAULT 'admin',
          is_active BOOLEAN NOT NULL DEFAULT true,
          last_login TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
      `);
      
      // Create indexes
      await sequelize.query(`
        CREATE INDEX IF NOT EXISTS idx_admins_username ON auth.admins(username);
        CREATE INDEX IF NOT EXISTS idx_admins_email ON auth.admins(email);
        CREATE INDEX IF NOT EXISTS idx_admins_role ON auth.admins(role);
        CREATE INDEX IF NOT EXISTS idx_admins_is_active ON auth.admins(is_active);
        CREATE INDEX IF NOT EXISTS idx_admins_created_at ON auth.admins(created_at);
      `);
      
      // Create trigger function
      await sequelize.query(`
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
      `);
      
      // Create trigger
      await sequelize.query(`
        CREATE TRIGGER update_admins_updated_at 
            BEFORE UPDATE ON auth.admins 
            FOR EACH ROW 
            EXECUTE FUNCTION update_updated_at_column();
      `);
      
      console.log('✅ Admin table created successfully');
    } else {
      console.log('✅ Admin table exists');
    }
    
    // Check if superadmin exists
    const [adminResults] = await sequelize.query(`
      SELECT username, email, role 
      FROM auth.admins 
      WHERE username = 'superadmin'
    `);
    
    if (adminResults.length === 0) {
      console.log('❌ Superadmin user does not exist. Creating...');
      
      // Hash the password 'admin123'
      const password_hash = await bcrypt.hash('admin123', 12);
      
      // Insert superadmin
      await sequelize.query(`
        INSERT INTO auth.admins (username, email, password_hash, full_name, role) 
        VALUES ('superadmin', '<EMAIL>', :password_hash, 'Super Administrator', 'superadmin')
      `, {
        replacements: { password_hash }
      });
      
      console.log('✅ Superadmin user created successfully');
      console.log('   Username: superadmin');
      console.log('   Email: <EMAIL>');
      console.log('   Password: admin123');
    } else {
      console.log('✅ Superadmin user exists:', adminResults[0]);
    }
    
    // List all admins
    const [allAdmins] = await sequelize.query(`
      SELECT username, email, role, is_active, created_at 
      FROM auth.admins 
      ORDER BY created_at
    `);
    
    console.log('\n📋 All admin users:');
    allAdmins.forEach(admin => {
      console.log(`  - ${admin.username} (${admin.email}) - Role: ${admin.role} - Active: ${admin.is_active}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Run the script
if (require.main === module) {
  checkAndCreateAdminTable()
    .then(() => {
      console.log('\n🎉 Admin table check completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Admin table check failed:', error.message);
      process.exit(1);
    });
}

module.exports = { checkAndCreateAdminTable };
