{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:24:36"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:26:05"}
{"error":"password authentication failed for user \"postgres\"","level":"error","message":"Health check failed","service":"auth-service","status":"unhealthy","timestamp":"2025-07-15 17:28:18"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:28:50"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:31:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:31:57"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:28"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:46"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:46"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:58"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:33:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:33:26"}
{"email":"<EMAIL>","error":"password authentication failed for user \"postgres\"","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 17:33:27"}
{"error":"password authentication failed for user \"postgres\"","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3bd0a84f-2c03-4720-8c3b-f53fc6426413","service":"auth-service","stack":"SequelizeConnectionError: password authentication failed for user \"postgres\"\n    at Client._connectionCallback (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:145:24)\n    at Client._handleErrorWhileConnecting (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\client.js:336:19)\n    at Client._handleErrorMessage (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\client.js:356:19)\n    at Connection.emit (node:events:518:28)\n    at D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\connection.js:116:12\n    at Parser.parse (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg-protocol\\dist\\parser.js:36:17)\n    at Socket.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg-protocol\\dist\\index.js:11:42)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)","timestamp":"2025-07-15 17:33:27","userAgent":"axios/1.10.0"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:34:43"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 18:47:58"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:08:19"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b5a42651-a7c9-452a-bc44-f3254f28bbec","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:08:19","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:08:41"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cd5dc5d1-b2c6-41b0-9714-ab5590cac3fa","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:08:41","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:10:34"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e6432726-eb9a-4f2c-b73b-fb407665aa5c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:10:34","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:11:17"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bbebde79-15d3-4866-9c4f-f6d844bce977","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:11:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:13:02"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"91f4529c-1757-4be0-bbaf-aa837095bd42","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:13:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:13:51"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"92ca9ade-1823-4b90-9b3d-9dca38f46107","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:13:51","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:14:45"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"0aeb1e9d-7a9b-442b-8295-502a04eaa0fd","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:14:45","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:16:17"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"c9239e33-6180-4569-8e83-7a9ec1c2b6f9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:16:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:20:58"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d0a44d01-d8de-491a-9b62-55017c0da149","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:20:58","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:27:52"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"612c9f01-812d-4b57-b089-5e71452dd96a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:27:52","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:34:09"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"192d655f-d6fa-4cb1-a07f-8cf98cb6ec5b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:34:09","userAgent":"axios/1.10.0"}
{"error":"Email already exists","ip":"::ffff:127.0.0.1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"fe9ffc0e-5be5-430a-ae54-b439a18f2e55","service":"auth-service","stack":"Error: Email already exists\n    at Object.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\tests\\auth.test.js:97:50)\n    at Promise.then.completed (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:444:34)","timestamp":"2025-07-16 07:56:26"}
{"error":"Invalid email or password","ip":"::ffff:127.0.0.1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"5c6d045a-d3a4-4d2e-9715-efa3d18df1e2","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\tests\\auth.test.js:177:47)\n    at Promise.then.completed (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:444:34)","timestamp":"2025-07-16 07:56:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-16 08:17:59"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-16 15:58:15"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"6720fadf-736c-430b-8e35-f0e466e5cf9e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-16 15:58:15","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:15:17"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"84d3593c-9cca-4f53-b7b5-6520b0290890","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:15:17","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:15:50"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"3dcace34-f05e-4a7e-996b-8fa371059b56","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:15:50","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:15:53"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"23247aa0-b791-40cc-a778-ee9c583f69f7","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:15:53","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:18:32"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"bfaaaab6-c7ae-45d7-845b-31d2a4514395","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:18:32","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:18:36"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"2040f2b4-99c4-42f0-9f0d-aedd02646eba","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:18:36","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:18:49"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"ee122e38-7816-40f7-a693-8e3d47486bf4","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:18:49","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:24:43"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"e3c3b757-d658-4824-a6d6-5f02c0d3ec4f","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:24:43","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-17 06:00:23"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"0404b631-1e62-4c16-87ce-5ee22f30db72","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-17 06:00:23","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Invalid value { username: 'superadmin' }","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:35:39","username":"superadmin"}
{"error":"Invalid value { username: 'superadmin' }","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"59f20432-3a02-4c8e-9e89-53b9d9dd2f13","service":"auth-service","stack":"Error: Invalid value { username: 'superadmin' }\n    at Object.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sql-string.js:54:11)\n    at PostgresQueryGenerator.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:766:22)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:71\n    at Array.map (<anonymous>)\n    at PostgresQueryGenerator._whereParseSingleValueObject (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:52)\n    at PostgresQueryGenerator.whereItemQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1812:19)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1737:25\n    at Array.forEach (<anonymous>)\n    at PostgresQueryGenerator.whereItemsQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1735:35)\n    at PostgresQueryGenerator.getWhereConditions (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:2078:19)","timestamp":"2025-07-18 05:35:39","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Invalid value { username: 'superadmin' }","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:36:04","username":"superadmin"}
{"error":"Invalid value { username: 'superadmin' }","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"e1bf6768-182f-4b28-a069-e388f3e25203","service":"auth-service","stack":"Error: Invalid value { username: 'superadmin' }\n    at Object.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sql-string.js:54:11)\n    at PostgresQueryGenerator.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:766:22)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:71\n    at Array.map (<anonymous>)\n    at PostgresQueryGenerator._whereParseSingleValueObject (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:52)\n    at PostgresQueryGenerator.whereItemQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1812:19)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1737:25\n    at Array.forEach (<anonymous>)\n    at PostgresQueryGenerator.whereItemsQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1735:35)\n    at PostgresQueryGenerator.getWhereConditions (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:2078:19)","timestamp":"2025-07-18 05:36:04","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:39:59","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"4953c00f-1f82-4b2d-95ac-4856292b29e1","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:39:59","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"request aborted","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","service":"auth-service","stack":"BadRequestError: request aborted\n    at IncomingMessage.onAborted (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\raw-body\\index.js:245:10)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage._destroy (node:_http_incoming:221:10)\n    at _destroy (node:internal/streams/destroy:122:10)\n    at IncomingMessage.destroy (node:internal/streams/destroy:84:5)\n    at abortIncoming (node:_http_server:811:9)\n    at socketOnClose (node:_http_server:805:3)\n    at Socket.emit (node:events:530:35)\n    at TCP.<anonymous> (node:net:346:12)","timestamp":"2025-07-18 05:47:41","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:47:48","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"3a1e19ea-5604-462e-96fe-c51aed2ca653","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:47:48","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
