const axios = require('axios');

async function testApiLogin() {
  try {
    console.log('🔍 Testing admin login via API...');
    
    const response = await axios.post('http://localhost:3000/admin/login', {
      username: 'superadmin',
      password: 'admin123'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Login successful!');
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ Login failed!');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run the script
testApiLogin();
